---
inclusion: always
---

# 项目结构与架构指南

## 核心系统约束

### 网格系统规格

- **33x33 网格**：1089 个单元格，零虚拟化全量渲染
- **8色系统**：红、青、黄、紫、橙、绿、蓝、粉（严格限制）
- **4层数据结构**：Level 1-4，基于0索引（坐标范围 0-32）
- **性能要求**：所有网格组件必须使用 `React.memo`，用户交互防抖 ≥100ms

### 必须遵循的架构原则

- 按功能组织，不按技术层级
- 层次结构：页面 → 功能 → UI 组件
- 禁止跨功能直接依赖，必须通过共享模块

## 状态管理架构 (Zustand)

### 实际存储架构

```typescript
// 当前项目的核心存储结构
MatrixStore        // 矩阵数据、配置、交互状态、词语绑定
WordLibraryStore   // 词库管理、词语验证、跨库重复检测
WordInputStore     // 填词模式、词语选择、临时状态
ToastStore         // 全局消息提示、多消息管理
```

### 存储职责分离

- **MatrixStore**: 33x33网格数据、单元格状态、配置管理、缓存计算
- **WordLibraryStore**: 8色4级词库、词语CRUD、重复检测、持久化
- **WordInputStore**: 填词交互、词语选择、临时预览状态
- **ToastStore**: UI反馈消息、成功/错误/警告提示

### 存储扩展规则

- **扩展现有存储**：功能与现有职责高度相关时
- **创建新存储**：独立功能域且状态复杂时
- **禁止**：跨存储直接状态依赖，使用订阅模式

## 项目架构结构

### 实际目录组织

```text
apps/frontend/
├── app/                    # Next.js 15 App Router页面
├── components/             # 共享UI组件
│   ├── ui/                # 基础UI组件库
│   ├── Matrix.tsx         # 核心矩阵组件
│   └── Controls.tsx       # 控制面板组件
├── core/                  # 核心业务逻辑
│   ├── matrix/           # 矩阵核心模块
│   ├── word/            # 词库核心模块
│   ├── ui/               # UI状态管理
│   └── data/             # 数据层
├── hooks/                # 共享React钩子
└── stores/               # 已废弃，迁移到core/
```

### 核心模块架构

- **core/matrix/**: MatrixStore + MatrixCore + MatrixTypes + MatrixConfig
- **core/word/**: WordStore + WordCore + WordInput + WordManager
- **core/ui/**: ToastStore等UI状态管理
- **core/data/**: GroupAData等静态数据管理

## 性能优化模式

### 必须应用的模式

```typescript
// 1. 所有网格组件使用 React.memo
const MatrixCell = React.memo(({ x, y }: CellProps) => {
  const cellData = useMatrixStore(state => state.getCellRenderData(x, y));
  // 组件逻辑
});

// 2. 使用Immer进行状态更新
set(produce((draft) => {
  draft.data.cells.set(key, updatedCell);
  draft.lastUpdate = Date.now();
}));

// 3. 缓存昂贵计算
const processedData = useMemo(() => {
  return matrixCore.processData(data, config);
}, [data, config]);

// 4. 事件处理器防抖
const handleCellClick = useMemo(() => 
  debounce((x: number, y: number) => {
    updateCell(x, y, updates);
  }, 100), []
);
```

## 文件组织规则

### 目录命名约定

- 功能目录：`kebab-case` (`grid-system`)
- 技术目录：`camelCase` (`components`, `hooks`)
- 最大嵌套深度：2层

### 文件放置决策

- 核心业务类型 → `core/[module]/[Module]Types.ts`
- 共享UI组件 → `components/ui/[Component].tsx`
- 页面特定组件 → `components/[Component].tsx`
- 业务逻辑钩子 → `hooks/[useHook].ts`
- 核心数据 → `core/data/[DataSource].ts`

### 导入顺序标准

```typescript
// 1. 外部库
import React from 'react'
import { create } from 'zustand'

// 2. 核心类型和工具
import type { MatrixConfig, CellData } from '@/core/matrix/MatrixTypes'
import { coordinateKey } from '@/core/matrix/MatrixTypes'

// 3. 核心存储和逻辑
import { useMatrixStore } from '@/core/matrix/MatrixStore'
import { useWordStore } from '@/core/word/WordStore'

// 4. 组件和钩子
import { Button } from '@/components/ui/Button'
import { useClickOutside } from '@/hooks/useClickOutside'
```

## 决策规则

### 组件放置决策

- 核心业务组件 → `components/[Component].tsx`
- 基础UI组件 → `components/ui/[Component].tsx`
- 页面特定组件 → `app/[page]/components/[Component].tsx`

### 状态管理选择

- 矩阵相关状态 → 扩展 `MatrixStore`
- 词库相关状态 → 扩展 `WordLibraryStore`
- UI反馈状态 → 使用 `Toast`
- 临时交互状态 → 使用 `useState` 或创建专用Store
- 需要持久化 → 使用Zustand persist中间件

### 核心模块扩展

- 矩阵功能 → 扩展 `core/matrix/` 模块
- 词库功能 → 扩展 `core/word/` 模块
- 新的独立功能域 → 创建 `core/[newModule]/` 模块

## 代码质量要求

### TypeScript 集成

- 所有组件 props 必须定义接口
- 启用严格模式
- 异步操作必须定义错误类型

### 错误处理

- 功能模块必须实现错误边界
- 组件失败必须提供回退 UI
- 禁止错误导致整个应用崩溃

### 导出策略

- 核心模块使用命名导出
- Store使用create()包装的默认导出
- 类型定义统一从Types文件导出
- 禁止循环依赖，使用依赖注入模式

## 关键架构模式

### 数据流架构

```typescript
// 单向数据流：UI → Store → Core → Data
UI组件 → MatrixStore → MatrixCore → GroupAData
     ↘ WordStore → WordCore
```

### 状态同步模式

- **矩阵-词库联动**: MatrixStore.cellWordBindings ↔ WordStore
- **词库清理机制**: 词库删除时自动清理矩阵绑定
- **跨存储通信**: 使用getState()而非直接依赖

### 性能优化策略

- **Map/Set数据结构**: 高效的坐标索引和去重
- **Immer状态更新**: 不可变状态的高性能更新
- **计算属性缓存**: MatrixStore.cache缓存渲染数据
- **持久化优化**: 仅持久化必要数据，运行时重建索引
