# Toast组件简化重构

## 概述

本次重构将原本分散的Toast系统（ToastStore.ts、Toast.tsx、ToastContainer.tsx）简化为单个文件实现，采用黑白灰设计风格，统一在矩阵系统中上方显示。

## 变更内容

### 1. 新增简化组件

**文件**: `apps/frontend/components/ui/SimpleToast.tsx`

- **单文件实现**: 包含状态管理、UI渲染和API接口
- **黑白灰设计**: 采用现代简约的单色配色方案
- **中上方显示**: 统一在页面中上方（top-16）位置显示
- **简约图标**: 使用简单的符号（✓、✕、!、i）

### 2. 设计风格

```typescript
const toastConfig = {
  success: {
    icon: '✓',
    bgColor: 'bg-gray-900',     // 深灰成功
    textColor: 'text-white',
    borderColor: 'border-gray-700'
  },
  error: {
    icon: '✕',
    bgColor: 'bg-black',        // 黑色错误
    textColor: 'text-white',
    borderColor: 'border-gray-800'
  },
  warning: {
    icon: '!',
    bgColor: 'bg-gray-200',     // 浅灰警告
    textColor: 'text-gray-800',
    borderColor: 'border-gray-400'
  },
  info: {
    icon: 'i',
    bgColor: 'bg-white',        // 白色信息
    textColor: 'text-gray-700',
    borderColor: 'border-gray-300'
  }
};
```

### 3. API简化

**新API**:
```typescript
import { toast } from '@/components/ui/Toast';

// 基础用法
toast.success('操作成功！');
toast.error('操作失败！');
toast.warning('注意事项');
toast.info('提示信息');

// 自定义持续时间
toast.success('短暂提示', 1000);
toast.warning('长时间提示', 5000);
```

**旧API对比**:
```typescript
// 旧版本（复杂配置）
toast.success('操作成功！', {
  duration: 3000,
  position: 'top-right',
  closable: true,
  className: 'custom'
});

// 新版本（简化）
toast.success('操作成功！', 3000);
```

### 4. 布局变更

- **位置**: 固定在矩阵坐标（16，12）位置
  - 计算公式：left: 16 * 34 + 6 = 550px, top: 12 * 34 + 6 = 414px
  - 使用 `transform: translate(-50%, -50%)` 居中对齐到坐标点
- **层级**: `z-50`
- **动画**: 简单的上下滑动效果
- **响应式**: 基于矩阵坐标系统的精确定位

### 5. 文件更新

**更新的文件**:
1. `apps/frontend/app/layout.tsx` - 替换ToastContainer为Toast
2. `apps/frontend/app/page.tsx` - 更新import和API调用
3. `apps/frontend/components/ui/CombinedWordLibraryInput.tsx` - 更新import和API调用

**新增的文件**:
1. `apps/frontend/components/ui/Toast.tsx` - 简化的单文件Toast组件（已重命名）
2. `apps/frontend/app/toast-test/page.tsx` - 测试页面

**删除的文件**:
1. `apps/frontend/components/ui/ToastStore.ts` - 旧的Toast状态管理
2. `apps/frontend/components/ui/Toast.tsx` - 旧的Toast组件
3. `apps/frontend/components/ui/ToastContainer.tsx` - 旧的Toast容器组件

## 使用指南

### 基础使用

```typescript
import { toast } from '@/components/ui/Toast';

// 在任何组件中调用
const handleSuccess = () => {
  toast.success('操作成功！');
};

const handleError = () => {
  toast.error('操作失败！');
};
```

### 测试

访问 `/toast-test` 页面可以测试所有Toast功能：
- 不同类型的消息显示
- 自定义持续时间
- 批量消息显示

## 优势

1. **简化维护**: 单文件实现，减少代码复杂度
2. **统一设计**: 黑白灰配色，符合简约设计理念
3. **固定位置**: 统一在中上方显示，用户体验一致
4. **轻量级**: 移除了复杂的位置配置和高级功能
5. **易于使用**: API更加简洁直观

## 迁移说明

原有的复杂Toast系统文件仍然保留，但不再使用：
- `apps/frontend/core/ui/ToastStore.ts`
- `apps/frontend/components/ui/Toast.tsx`
- `apps/frontend/components/ui/ToastContainer.tsx`

如需要高级功能（如多位置显示、复杂配置），可以考虑恢复使用原有系统。

## 技术特性

- **状态管理**: 基于Zustand的轻量级状态管理
- **动画效果**: CSS transition实现的流畅动画
- **自动清理**: 支持自定义持续时间的自动移除
- **手动关闭**: 点击×按钮手动关闭
- **无障碍**: 支持键盘操作和屏幕阅读器
