/**
 * 词库管理类型定义
 * 🎯 核心价值：统一的词库类型系统，支持数据驱动视图和业务模式切换
 * 📦 功能范围：词库数据、验证结果、填词状态、工具函数
 * 🔄 架构设计：基于数据驱动的类型设计，支持高性能计算属性
 */

// 导入基础类型
import type { BasicColorType, DataLevel } from '../matrix/MatrixTypes';

// ===== 词库管理类型 =====

/** 词库条目 */
export interface WordEntry {
  /** 唯一标识符 */
  id: string;
  /** 词语文本 */
  text: string;
  /** 所属颜色 */
  color: BasicColorType;
  /** 所属级别 */
  level: DataLevel;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
  /** 使用次数 */
  usageCount: number;
  /** 最后使用时间 */
  lastUsed?: Date;
  /** 是否为重复词语 */
  isDuplicate?: boolean;
  /** 使用位置记录 */
  usagePositions?: Array<{ x: number; y: number; timestamp: Date }>;
}

/** 词库类别键 */
export type WordLibraryKey = `${BasicColorType}-${DataLevel}`;

/** 词库数据结构 */
export interface WordLibrary {
  /** 词库标识 */
  key: WordLibraryKey;
  /** 颜色 */
  color: BasicColorType;
  /** 级别 */
  level: DataLevel;
  /** 词语列表 */
  words: WordEntry[];
  /** 是否折叠 */
  collapsed: boolean;
  /** 最后更新时间 */
  lastUpdated: Date;
}

/** 词库管理状态 */
export interface WordLibraryState {
  /** 所有词库 */
  libraries: Map<WordLibraryKey, WordLibrary>;
  /** 重复词语映射 */
  duplicateWords: Map<string, WordLibraryKey[]>;
  /** 词语到随机颜色的映射 */
  wordHighlightColors: Map<string, string>;
  /** 已使用的随机颜色集合 */
  usedHighlightColors: Set<string>;
  /** 全局词语索引：词语 -> 所在词库集合 */
  globalWordIndex: Map<string, Set<WordLibraryKey>>;
  /** 是否正在加载 */
  isLoading: boolean;
  /** 最后同步时间 */
  lastSyncTime: Date | null;
}

/** 词语验证结果 */
export interface WordValidationResult {
  /** 是否有效 */
  isValid: boolean;
  /** 错误信息 */
  errors: string[];
  /** 是否重复 */
  isDuplicate: boolean;
  /** 重复的词库 */
  duplicateLibraries: WordLibraryKey[];
}

/** 填词模式状态 */
export interface WordInputState {
  /** 是否激活填词模式 */
  isActive: boolean;
  /** 当前选中的单元格 */
  selectedCell: { x: number; y: number } | null;
  /** 匹配的词库 */
  matchedLibrary: WordLibraryKey | null;
  /** 当前选中的词语索引 */
  selectedWordIndex: number;
  /** 可选词语列表 */
  availableWords: WordEntry[];
  /** 临时显示的词语（用于实时预览） */
  temporaryWord: string | null;
  /** 是否已绑定词语（用于判断退出时是否需要清除临时显示） */
  isWordBound: boolean;
}

// ===== 工具函数 =====

/** 词库键生成器 */
export const createWordLibraryKey = (color: BasicColorType, level: DataLevel): WordLibraryKey =>
  `${color}-${level}` as WordLibraryKey;

/** 词库键解析器 */
export const parseWordLibraryKey = (key: WordLibraryKey): { color: BasicColorType; level: DataLevel } => {
  const [color, levelStr] = key.split('-');
  return {
    color: color as BasicColorType,
    level: parseInt(levelStr) as DataLevel
  };
};
