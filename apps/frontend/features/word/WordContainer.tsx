/**
 * 词库管理主组件
 * 🎯 核心价值：统一的词库管理界面，支持29个词库的可视化管理
 * 📦 功能范围：词库列表、折叠展开、颜色分类、词语管理
 * 🔄 架构设计：基于状态驱动的响应式组件，支持实时更新
 */

'use client';

import Button from '@/components/ui/Button';
import type { BasicColorType, DataLevel } from '@/features/matrix/MatrixTypes';
import { AVAILABLE_WORD_LIBRARIES, getWordLibraryDisplayName } from '@/features/word/WordConfig';
import WordInput from '@/features/word/WordInputUI';
import { useWordInputStore, useWordStore } from '@/features/word/WordStore';
import type { WordLibraryKey } from '@/features/word/WordTypes';
import { useContainerHeight, WORD_MANAGER_HEIGHT_CONFIG } from '@/hooks/useContainerHeight';
import React, { memo, useCallback, useEffect, useMemo, useRef } from 'react';

// ===== 组件属性 =====

interface WordLibraryManagerProps {
  /** 自定义类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 是否为颜色词语模式（可选，用于外部控制） */
  isColorWordMode?: boolean;
}

// ===== 词库项组件 =====

interface WordLibraryItemProps {
  color: BasicColorType;
  level: DataLevel;
  libraryKey: WordLibraryKey;
}

const WordLibraryItem: React.FC<WordLibraryItemProps> = ({ color, level, libraryKey }) => {
  const itemRef = useRef<HTMLDivElement>(null);
  const {
    getLibrary
  } = useWordStore();

  // 获取填词模式状态
  const { isActive: isWordInputActive, matchedLibrary } = useWordInputStore();

  const library = getLibrary(libraryKey);
  const displayName = getWordLibraryDisplayName(color, level);

  // 检查是否为当前使用的词库
  const isActiveLibrary = useMemo(() => {
    return isWordInputActive && matchedLibrary === libraryKey;
  }, [isWordInputActive, matchedLibrary, libraryKey]);

  // 简化的滑动逻辑 - 当词库被激活时滑动到可视区域
  useEffect(() => {
    if (isActiveLibrary && itemRef.current) {
      // 延迟一小段时间确保DOM更新完成
      const timer = setTimeout(() => {
        if (itemRef.current) {
          itemRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest'
          });
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [isActiveLibrary]);

  if (!library) return null;

  return (
    <div
      ref={itemRef}
      className={`mb-3 transition-all duration-200 ${isActiveLibrary ? 'ring-2 ring-blue-400 ring-opacity-50 rounded-lg' : ''}`}
      data-word-library={libraryKey}
    >
      {/* 词库输入组件 */}
      <WordInput
        libraryKey={libraryKey}
        color={color}
        level={level}
        collapsed={library.collapsed}
        placeholder={`输入${displayName}词语...`}
        className={isActiveLibrary ? 'word-library-active shadow-md' : ''}
      />
    </div>
  );
};



// ===== 主组件 =====

const WordLibraryManagerComponent: React.FC<WordLibraryManagerProps> = ({
  className = '',
  style,
  isColorWordMode = true // 默认为true，保持向后兼容
}) => {
  const { resetAllLibraries, exportData, importData } = useWordStore();

  // 使用容器高度计算Hook
  const { containerHeight, containerRef, recalculate } = useContainerHeight(WORD_MANAGER_HEIGHT_CONFIG);

  // 处理导出
  const handleExport = useCallback(() => {
    const data = exportData();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `词库数据_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [exportData]);

  // 处理导入
  const handleImport = useCallback(() => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const data = e.target?.result as string;
          const success = importData(data);
          if (success) {
            alert('导入成功！');
          } else {
            alert('导入失败，请检查文件格式。');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  }, [importData]);

  // 处理重置
  const handleReset = useCallback(() => {
    if (confirm('确定要清空所有词库吗？此操作不可撤销。')) {
      resetAllLibraries();
    }
  }, [resetAllLibraries]);

  // 如果不是【颜色】【词语】模式，显示提示
  if (!isColorWordMode) {
    return (
      <div className={`word-library-manager ${className} flex flex-col h-full items-center justify-center`} style={style}>
        <div className="text-center text-gray-500">
          <p className="text-sm">词库管理功能仅在【颜色】【词语】模式下可用</p>
          <p className="text-xs mt-1">请切换到颜色模式 + 词语内容模式</p>
        </div>
      </div>
    );
  }

  // 当词库数量变化时重新计算高度
  useEffect(() => {
    recalculate();
  }, [AVAILABLE_WORD_LIBRARIES.length, recalculate]);

  return (
    <div
      ref={containerRef}
      className={`word-library-manager ${className} flex flex-col h-full`}
      style={style}
    >
      {/* 标题栏 */}
      <div className="flex items-center justify-between mb-6 flex-shrink-0">
        <h3 className="text-lg font-semibold text-gray-800">词库管理</h3>
        <div className="flex space-x-2">
          <Button
            variant="secondary"
            size="sm"
            onClick={handleExport}
            title="导出词库数据"
          >
            导出
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={handleImport}
            title="导入词库数据"
          >
            导入
          </Button>
          <Button
            variant="danger"
            size="sm"
            onClick={handleReset}
            title="清空所有词库"
          >
            清空
          </Button>
        </div>
      </div>

      {/* 词库列表 - 使用计算出的高度，确保29个词库能在一个页面容器内显示 */}
      <div
        className="word-library-manager overflow-y-auto pr-2 space-y-1"
        style={{
          height: `${containerHeight}px`
        }}
      >
        <div className="space-y-3">
          {AVAILABLE_WORD_LIBRARIES.map(({ color, level }) => {
            const libraryKey = `${color}-${level}` as WordLibraryKey;
            return (
              <WordLibraryItem
                key={libraryKey}
                color={color}
                level={level}
                libraryKey={libraryKey}
              />
            );
          })}
        </div>
      </div>
    </div>
  );
};

// ===== 性能优化 =====

const WordManager = memo(WordLibraryManagerComponent);

WordManager.displayName = 'WordManager';

export default WordManager;
export type { WordLibraryManagerProps };
