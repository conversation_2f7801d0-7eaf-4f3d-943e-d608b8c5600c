/**
 * 词库输入组件
 * 🎯 核心价值：一体化词库输入框，支持展开/折叠和横向滚动
 * 📦 功能范围：词库标题显示、词语列表展示、输入框、展开折叠控制
 * 🔄 架构设计：一体化容器设计，与矩阵系统颜色同步
 */

'use client';

import { toast } from '@/components/ui/Toast';
import type { BasicColorType, DataLevel } from '@/features/matrix/MatrixTypes';
import {
  getWordLibraryBackgroundColor,
  getWordLibraryDisplayName,
  getWordLibraryShortName,
  getWordLibraryTextColor
} from '@/features/word/WordConfig';
import { parseInputText } from '@/features/word/WordHook';
import { useWordInputStore, useWordStore } from '@/features/word/WordStore';
import type { WordLibraryKey } from '@/features/word/WordTypes';
import React, { useCallback, useRef, useState } from 'react';

// ===== 组件属性 =====

interface WordInputProps {
  /** 词库标识 */
  libraryKey: WordLibraryKey;
  /** 颜色 */
  color: BasicColorType;
  /** 级别 */
  level: DataLevel;
  /** 是否折叠状态 */
  collapsed?: boolean;
  /** 占位符文本 */
  placeholder?: string;
  /** 自定义类名 */
  className?: string;
  /** 输入变化回调 */
  onChange?: (words: string[]) => void;
}



// ===== 主组件 =====

const WordInput: React.FC<WordInputProps> = ({
  libraryKey,
  color,
  level,
  collapsed = false,
  placeholder = '输入词语，用逗号分隔...',
  className = '',
  onChange
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [inputValue, setInputValue] = useState('');
  const [isExpanded, setIsExpanded] = useState(!collapsed);
  const [editingWordId, setEditingWordId] = useState<string | null>(null);
  const [editingText, setEditingText] = useState('');

  // 获取词库状态
  const {
    getLibrary,
    validateInput,
    addWord,
    removeWord,
    updateWord,
    toggleLibraryCollapse,
    checkCrossLibraryDuplicate,
    getWordHighlightColor
  } = useWordStore();

  // 获取填词模式状态
  const { temporaryWord } = useWordInputStore();

  const library = getLibrary(libraryKey);
  const displayName = getWordLibraryDisplayName(color, level);
  const shortName = getWordLibraryShortName(color, level);
  const backgroundColor = getWordLibraryBackgroundColor(color);
  const textColor = getWordLibraryTextColor(color);

  // 显示错误提示
  const showErrorMessage = useCallback((message: string) => {
    toast.controlPanel.error(message, 3000);
  }, []);

  // 处理输入确认（逗号或回车）
  const handleInputConfirm = useCallback(() => {
    if (!inputValue.trim()) return;

    // 解析输入文本，过滤空词语
    const newWords = parseInputText(inputValue).filter(word => word.trim().length > 0);
    if (newWords.length === 0) {
      setInputValue('');
      return;
    }

    const validWords: string[] = [];
    const errorWords: string[] = [];
    const duplicateWords: string[] = [];

    // 逐个验证和添加词语
    newWords.forEach(word => {
      const trimmedWord = word.trim();

      // 先进行输入验证（阻止性验证）
      const validation = validateInput(libraryKey, trimmedWord);
      if (validation.isValid) {
        // 验证通过，尝试添加词语
        const result = addWord(libraryKey, trimmedWord);
        if (result.isValid) {
          validWords.push(trimmedWord);

          // 检查是否为跨词库重复词语，显示提醒（提醒性检测）
          if (result.isDuplicate && result.duplicateLibraries && result.duplicateLibraries.length > 1) {
            const otherLibraries = result.duplicateLibraries.filter(lib => lib !== libraryKey);
            if (otherLibraries.length > 0) {
              duplicateWords.push(trimmedWord);
            }
          }
        } else {
          // 添加失败（通常是同词库内重复）
          errorWords.push(trimmedWord);
        }
      } else {
        // 验证失败
        errorWords.push(trimmedWord);
        console.warn(`词语"${trimmedWord}"验证失败:`, validation.errors);
      }
    });

    // 清空输入框
    setInputValue('');

    // 显示提示信息
    if (errorWords.length > 0) {
      showErrorMessage(`无效或重复词语: ${errorWords.join(', ')}`);
    }

    if (duplicateWords.length > 0) {
      toast.controlPanel.warning(`词语"${duplicateWords.join('", "')}"在其他词库中存在`, 3000);
    }

    if (validWords.length > 0) {
      toast.controlPanel.success(`成功添加 ${validWords.length} 个词语`, 2000);
    }

    // 触发回调
    if (validWords.length > 0) {
      onChange?.(validWords);
    }
  }, [inputValue, libraryKey, validateInput, addWord, onChange, showErrorMessage]);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;

    // 保持中文逗号格式，统一处理
    value = value.replace(/,/g, '，');

    setInputValue(value);

    // 实时检测逗号并自动确认输入（优化：避免二次触发）
    if (value.endsWith('，') && value.trim().length > 1) {
      // 检查逗号前是否有有效词语
      const beforeComma = value.slice(0, -1).trim();
      if (beforeComma.length > 0) {
        // 延迟处理，避免用户快速输入时的冲突
        setTimeout(() => {
          // 再次检查当前输入值，确保用户没有继续输入
          if (inputRef.current && inputRef.current.value.endsWith('，')) {
            handleInputConfirm();
          }
        }, 300);
      }
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();

      // Enter键处理：确保最后一个词后面也有逗号
      if (inputValue.trim()) {
        let formattedValue = inputValue.trim();

        // 如果不以逗号结尾，自动补逗号
        if (!formattedValue.endsWith(',') && !formattedValue.endsWith('，')) {
          formattedValue += '，';
        }

        // 统一格式化为中文逗号（保持一致性）
        formattedValue = formattedValue.replace(/,\s*/g, '，');

        setInputValue(formattedValue);

        // 延迟确认，让用户看到格式化后的逗号
        setTimeout(() => {
          handleInputConfirm();
        }, 200);
      } else {
        // 如果为空，直接确认
        handleInputConfirm();
      }
    } else if (e.key === ',' || e.key === '，') {
      // 逗号键不阻止默认行为，让输入变化事件处理实时识别
      // 这样可以实现更灵敏的识别
    }
  };

  // 删除词语
  const handleRemoveWord = (wordId: string) => {
    if (library) {
      removeWord(libraryKey, wordId);
    }
  };

  // 切换展开/折叠
  const handleToggleExpanded = () => {
    setIsExpanded(!isExpanded);
    toggleLibraryCollapse(libraryKey);
  };

  // 开始编辑词语
  const handleStartEdit = (wordId: string, currentText: string) => {
    if (isExpanded) {
      setEditingWordId(wordId);
      setEditingText(currentText);
    }
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setEditingWordId(null);
    setEditingText('');
  };

  // 保存编辑
  const handleSaveEdit = () => {
    if (editingWordId && editingText.trim()) {
      const result = updateWord(libraryKey, editingWordId, editingText.trim());

      if (result.isValid) {
        toast.controlPanel.success('词语更新成功', 2000);
        if (result.isDuplicate && result.duplicateLibraries.length > 1) {
          toast.controlPanel.warning(`词语"${editingText.trim()}"在其他词库中存在`, 3000);
        }
      } else {
        showErrorMessage(`更新失败: ${result.errors.join(', ')}`);
      }
    }

    handleCancelEdit();
  };

  // 处理编辑输入键盘事件
  const handleEditKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancelEdit();
    }
  };

  if (!library) {
    return null;
  }

  // 动态生成滚动条样式
  const scrollbarStyles = React.useMemo(() => {
    const styleId = `word-scrollbar-${libraryKey}`;
    return {
      styleId,
      css: `
        .word-list-horizontal-${libraryKey}::-webkit-scrollbar {
          height: 4px;
        }
        .word-list-horizontal-${libraryKey}::-webkit-scrollbar-track {
          background: transparent;
        }
        .word-list-horizontal-${libraryKey}::-webkit-scrollbar-thumb {
          background: ${backgroundColor}60;
          border-radius: 2px;
        }
        .word-list-horizontal-${libraryKey}::-webkit-scrollbar-thumb:hover {
          background: ${backgroundColor}80;
        }
      `
    };
  }, [backgroundColor, libraryKey]);

  // 动态注入样式
  React.useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.id = scrollbarStyles.styleId;
    styleElement.textContent = scrollbarStyles.css;
    document.head.appendChild(styleElement);

    return () => {
      const existingStyle = document.getElementById(scrollbarStyles.styleId);
      if (existingStyle) {
        document.head.removeChild(existingStyle);
      }
    };
  }, [scrollbarStyles]);

  return (
    <div
      ref={containerRef}
      className={`word-input-container relative rounded-lg border transition-all duration-200 ${className}`}
      style={{
        backgroundColor: backgroundColor + '20',
        borderColor: backgroundColor + '40',
        color: textColor
      }}
    >
      {/* 主容器 - 一体化布局 */}
      <div className="p-4">
        {/* 统一的词语展示区域 */}
        <div className="flex items-center gap-3">
          {/* 折叠/展开按钮 */}
          <button
            onClick={handleToggleExpanded}
            className="flex-shrink-0 w-6 h-6 flex items-center justify-center text-sm hover:bg-black hover:bg-opacity-10 rounded-md transition-colors"
            style={{ color: textColor }}
            title={isExpanded ? '折叠词库' : '展开词库'}
          >
            {isExpanded ? '−' : '+'}
          </button>

          {/* 词语展示容器 - 根据展开状态调整滚动行为 */}
          <div
            className={`flex-1 word-list-horizontal-${libraryKey} ${!isExpanded ? 'overflow-x-auto' : 'overflow-x-visible'}`}
            style={{
              scrollbarWidth: 'thin',
              scrollbarColor: `${backgroundColor}60 transparent`,
              maxWidth: '100%',
              // 折叠状态下启用横向滚动，展开状态下允许换行
              overflowY: isExpanded ? 'visible' : 'hidden',
              minHeight: isExpanded ? 'auto' : '32px' // 确保展开状态有足够高度换行
            }}
          >
            <div className={`flex items-center gap-0 text-sm ${isExpanded ? 'flex-wrap' : 'flex-nowrap'}`} style={{
              // 展开状态下确保有足够宽度进行换行
              width: isExpanded ? '100%' : 'max-content'
            }}>
              {/* 词库标题部分 - 加粗显示 */}
              <span className="font-bold mr-1 flex-shrink-0" style={{ color: textColor }}>
                {shortName}[{library.words.length}词]：
              </span>

              {/* 已有词语列表 */}
              {library.words.map((word, index) => {
                // 检查是否为矩阵系统选中的词语
                const isSelected = temporaryWord === word.text;
                // 检查是否为跨词库重复词语
                const duplicateCheck = checkCrossLibraryDuplicate(word.text);
                const isDuplicate = duplicateCheck.isDuplicate && duplicateCheck.duplicateLibraries.length > 1;
                const duplicateColor = isDuplicate ? getWordHighlightColor(word.text) : undefined;

                return (
                  <React.Fragment key={word.id}>
                    {editingWordId === word.id ? (
                      // 编辑状态 - 只显示光标，无任何样式
                      <span className="inline-flex items-center px-2 py-1 flex-shrink-0">
                        <input
                          type="text"
                          value={editingText}
                          onChange={(e) => setEditingText(e.target.value)}
                          onKeyDown={handleEditKeyDown}
                          onBlur={handleSaveEdit}
                          autoFocus
                          style={{
                            color: textColor,
                            fontSize: '14px',
                            width: `${Math.max(editingText.length * 8 + 20, 60)}px`,
                            padding: '0',
                            margin: '0',
                            border: 'none',
                            outline: 'none',
                            background: 'transparent',
                            backgroundColor: 'transparent',
                            boxShadow: 'none',
                            appearance: 'none',
                            WebkitAppearance: 'none',
                            MozAppearance: 'textfield'
                          }}
                        />
                      </span>
                    ) : (
                      // 正常显示状态 - hover删除功能在所有状态下都可用
                      <span
                        className="group inline-flex items-center px-2 py-1 rounded-md transition-all duration-200 flex-shrink-0 hover:bg-black hover:bg-opacity-10 cursor-pointer"
                        style={{
                          color: textColor,
                          fontSize: '14px',
                          textDecoration: isSelected ? 'underline' : 'none',
                          backgroundColor: isDuplicate && duplicateColor ? duplicateColor + '30' : 'transparent',
                          border: isSelected ? `1px solid ${textColor}40` : '1px solid transparent',
                          borderRadius: '6px' // 确保圆角样式
                        }}
                        title={`${word.text} [使用${word.usagePositions?.length || 0}次] - 双击编辑，悬停显示删除按钮`}
                        onDoubleClick={() => handleStartEdit(word.id, word.text)}
                      >
                        <span className="word-text">{word.text}[{word.usagePositions?.length || 0}]</span>
                        {/* 删除按钮在所有状态下都显示，通过hover控制可见性 */}
                        <span
                          className="ml-1 text-red-500 hover:text-red-700 text-xs font-bold opacity-0 group-hover:opacity-100 transition-opacity duration-200 cursor-pointer"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemoveWord(word.id);
                          }}
                          title="删除词语"
                        >
                          ×
                        </span>
                      </span>
                    )}
                    <span style={{ color: textColor, fontSize: '14px', flexShrink: 0 }}>，</span>
                  </React.Fragment>
                );
              })}

              {/* 完全一体化的输入框 - 只显示光标，无任何样式 */}
              <input
                ref={inputRef}
                type="text"
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                onBlur={handleInputConfirm}
                placeholder={library.words.length === 0 ? placeholder : '输入...'}
                className="flex-shrink-0"
                style={{
                  color: textColor,
                  fontSize: '14px',
                  fontFamily: 'inherit',
                  minWidth: library.words.length === 0 ? '200px' : '60px',
                  maxWidth: '200px',
                  padding: '0',
                  margin: '0',
                  border: 'none',
                  outline: 'none',
                  background: 'transparent',
                  backgroundColor: 'transparent',
                  boxShadow: 'none',
                  appearance: 'none',
                  WebkitAppearance: 'none',
                  MozAppearance: 'textfield'
                }}
              />
            </div>
          </div>
        </div>


      </div>
    </div>
  );
};

export default WordInput;
export type { WordInputProps };
