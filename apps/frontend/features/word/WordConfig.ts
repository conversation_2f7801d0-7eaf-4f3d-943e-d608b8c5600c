/**
 * 词库配置常量
 * 🎯 核心价值：统一的词库配置管理，避免重复定义
 * 📦 功能范围：词语验证规则、颜色配置、显示名称映射
 * 🔄 架构设计：集中化配置管理，便于维护和修改
 */

import { AVAILABLE_LEVELS, DEFAULT_COLOR_VALUES } from '../../stores/GroupAData';
import type { BasicColorType, DataLevel } from '../matrix/MatrixTypes';

// ===== 词语验证配置 =====

/** 词语长度限制 */
export const WORD_LENGTH_LIMITS = {
  /** 最小长度 */
  MIN: 2,
  /** 最大长度 */
  MAX: 4
} as const;

/** 词语验证正则表达式 */
export const WORD_VALIDATION_REGEX = {
  /** 中文字符检测 */
  CHINESE: /[\u4e00-\u9fff]/,
  /** 特殊字符检测（不允许的标点符号） */
  SPECIAL_CHARS: /[，。！？；：""''（）【】《》]/
} as const;

// ===== 颜色配置 =====

/** 支持的颜色顺序（按UI显示顺序） */
export const COLOR_DISPLAY_ORDER: BasicColorType[] = [
  'black', 'red', 'orange', 'yellow', 'green', 'cyan', 'blue', 'purple', 'pink'
];

/** 颜色显示名称映射 */
export const COLOR_DISPLAY_NAMES: Record<BasicColorType, string> = {
  black: '黑',
  red: '红',
  orange: '橙',
  yellow: '黄',
  green: '绿',
  cyan: '青',
  blue: '蓝',
  purple: '紫',
  pink: '粉'
} as const;

/** 重复词语高亮颜色池 */
export const DUPLICATE_HIGHLIGHT_COLORS = [
  '#ffeb3b', '#ff9800', '#e91e63', '#9c27b0',
  '#673ab7', '#3f51b5', '#2196f3', '#00bcd4',
  '#009688', '#4caf50', '#8bc34a', '#cddc39',
  '#ffc107', '#ff5722', '#795548', '#607d8b'
] as const;

// ===== 词库配置 =====

/** 所有可用的词库配置 */
export const AVAILABLE_WORD_LIBRARIES: Array<{ color: BasicColorType; level: DataLevel }> = [];

// 初始化可用词库列表
COLOR_DISPLAY_ORDER.forEach(color => {
  const availableLevels = AVAILABLE_LEVELS[color];
  availableLevels.forEach(level => {
    AVAILABLE_WORD_LIBRARIES.push({ color, level: level as DataLevel });
  });
});

// ===== 工具函数 =====

/**
 * 获取词库显示名称
 * @param color 颜色类型
 * @param level 数据级别
 * @returns 格式化的显示名称
 */
export const getWordLibraryDisplayName = (color: BasicColorType, level: DataLevel): string => {
  return `${COLOR_DISPLAY_NAMES[color]}${level}级`;
};

/**
 * 获取简化的词库显示名称
 * @param color 颜色类型
 * @param level 数据级别
 * @returns 简化的显示名称，如：黑1、红2
 */
export const getWordLibraryShortName = (color: BasicColorType, level: DataLevel): string => {
  return `${COLOR_DISPLAY_NAMES[color]}${level}`;
};

/**
 * 获取词库背景色（与矩阵系统同步）
 * @param color 颜色类型
 * @returns 十六进制颜色值
 */
export const getWordLibraryBackgroundColor = (color: BasicColorType): string => {
  return DEFAULT_COLOR_VALUES[color].hex;
};

/**
 * 获取文字颜色（基于背景色自动适配）
 * @param backgroundColor 背景色
 * @returns 适配的文字颜色
 */
export const getWordLibraryTextColor = (backgroundColor: string): string => {
  // 简单的亮度检测
  const isDark = backgroundColor === '#000000' ||
    (backgroundColor.startsWith('#') && parseInt(backgroundColor.slice(1), 16) < 0x808080);

  return isDark ? '#ffffff' : '#000000';
};

/**
 * 生成随机高亮颜色
 * @param usedColors 已使用的颜色集合
 * @returns 随机高亮颜色
 */
export const generateRandomHighlightColor = (usedColors: Set<string>): string => {
  const availableColors = DUPLICATE_HIGHLIGHT_COLORS.filter(color => !usedColors.has(color));

  if (availableColors.length === 0) {
    // 如果所有颜色都用完了，重新开始
    return DUPLICATE_HIGHLIGHT_COLORS[Math.floor(Math.random() * DUPLICATE_HIGHLIGHT_COLORS.length)];
  }

  return availableColors[Math.floor(Math.random() * availableColors.length)];
};

/**
 * 为词语分配新的高亮颜色
 * @param usedColors 已使用的颜色集合
 * @returns 新的高亮颜色
 */
export const assignWordHighlightColor = (usedColors: Set<string>): string => {
  return generateRandomHighlightColor(usedColors);
};

// ===== 导出类型 =====

export type WordLengthLimitsType = typeof WORD_LENGTH_LIMITS;
export type ColorDisplayOrderType = typeof COLOR_DISPLAY_ORDER;
export type DuplicateHighlightColorsType = typeof DUPLICATE_HIGHLIGHT_COLORS;
