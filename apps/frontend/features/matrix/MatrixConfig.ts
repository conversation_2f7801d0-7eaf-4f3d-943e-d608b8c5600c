/**
 * 矩阵配置常量
 * 🎯 核心价值：统一的矩阵尺寸和样式配置，避免重复定义
 * 📦 功能范围：矩阵尺寸、单元格样式、响应式断点配置
 * 🔄 架构设计：集中化配置管理，便于维护和修改
 */

// ===== 基础矩阵配置 =====

/** 矩阵基础配置 */
export const MATRIX_CONFIG = {
  /** 矩阵尺寸 */
  SIZE: 33,
  /** 单元格基础尺寸 */
  CELL_SIZE: 33,
  /** 增强单元格尺寸（1级格子） */
  ENHANCED_CELL_SIZE: 39,
  /** 单元格间距 */
  CELL_GAP: 1,
  /** 容器内边距 */
  CONTAINER_PADDING: 6,
  /** 基础矩阵尺寸（33 * 34px） */
  BASE_SIZE: 1122,
  /** 容器扩展尺寸（为1级格子预留空间） */
  CONTAINER_EXPANSION: 12,
} as const;

// ===== 计算属性 =====

/** 矩阵实际尺寸（包含扩展） */
export const MATRIX_ACTUAL_SIZE = MATRIX_CONFIG.BASE_SIZE + MATRIX_CONFIG.CONTAINER_EXPANSION;

/** 增强格子的偏移量 */
export const ENHANCED_CELL_OFFSET = (MATRIX_CONFIG.ENHANCED_CELL_SIZE - MATRIX_CONFIG.CELL_SIZE) / 2;

// ===== 响应式配置 =====

/** 响应式断点配置 */
export const RESPONSIVE_BREAKPOINTS = {
  /** 移动设备 */
  MOBILE: 768,
  /** 平板设备 */
  TABLET: 1024,
  /** 桌面设备 */
  DESKTOP: 1024,
  /** 控制面板宽度 */
  CONTROLS_WIDTH: 320,
  /** 最小边距 */
  MIN_MARGIN: 20,
} as const;

/** 自动隐藏断点 */
export const AUTO_HIDE_BREAKPOINT = MATRIX_ACTUAL_SIZE + RESPONSIVE_BREAKPOINTS.CONTROLS_WIDTH + RESPONSIVE_BREAKPOINTS.MIN_MARGIN;

// ===== 样式配置 =====

/** 基础单元格样式 */
export const BASE_CELL_STYLE = {
  position: 'absolute' as const,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  cursor: 'pointer',
  userSelect: 'none' as const,
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  whiteSpace: 'nowrap' as const,
  transition: 'width 0.2s ease, height 0.2s ease, border 0.2s ease, border-radius 0.2s ease, z-index 0.2s ease',
  borderRadius: '4px',
  zIndex: 1,
} as const;

/** 增强单元格样式（1级格子） */
export const ENHANCED_CELL_STYLE = {
  ...BASE_CELL_STYLE,
  borderRadius: '50%',
  zIndex: 10,
} as const;

// ===== 工具函数 =====

/**
 * 获取单元格样式
 * @param isEnhanced 是否为增强格子（1级格子）
 * @param position 格子位置
 * @returns 单元格样式对象
 */
export const getCellStyle = (isEnhanced: boolean, position: { x: number; y: number }) => {
  const cellSize = isEnhanced ? MATRIX_CONFIG.ENHANCED_CELL_SIZE : MATRIX_CONFIG.CELL_SIZE;
  const offset = isEnhanced ? -ENHANCED_CELL_OFFSET : 0;
  
  return {
    ...(isEnhanced ? ENHANCED_CELL_STYLE : BASE_CELL_STYLE),
    width: `${cellSize}px`,
    height: `${cellSize}px`,
    left: `${position.x * 34 + offset + MATRIX_CONFIG.CONTAINER_PADDING}px`,
    top: `${position.y * 34 + offset + MATRIX_CONFIG.CONTAINER_PADDING}px`,
  };
};

/**
 * 获取容器样式
 * @returns 容器样式对象
 */
export const getContainerStyle = () => ({
  position: 'relative' as const,
  width: `${MATRIX_ACTUAL_SIZE}px`,
  height: `${MATRIX_ACTUAL_SIZE}px`,
  padding: `${MATRIX_CONFIG.CONTAINER_PADDING}px`,
  boxSizing: 'border-box' as const,
  userSelect: 'none' as const,
  minWidth: `${MATRIX_ACTUAL_SIZE}px`,
  minHeight: `${MATRIX_ACTUAL_SIZE}px`,
  transition: 'width 0.2s ease, height 0.2s ease, padding 0.2s ease',
});

/**
 * 获取视口样式
 * @param customStyle 自定义样式
 * @returns 视口样式对象
 */
export const getViewportStyle = (customStyle: React.CSSProperties = {}) => ({
  width: '100%',
  height: '100%',
  aspectRatio: '1 / 1' as const,
  overflow: 'auto' as const,
  borderRadius: '6px',
  transition: 'max-width 0.2s ease, max-height 0.2s ease',
  maxWidth: `${MATRIX_ACTUAL_SIZE}px`,
  maxHeight: `${MATRIX_ACTUAL_SIZE}px`,
  ...customStyle,
});

// ===== 导出类型 =====

export type MatrixConfigType = typeof MATRIX_CONFIG;
export type ResponsiveBreakpointsType = typeof RESPONSIVE_BREAKPOINTS;
