/**
 * 统一矩阵状态管理
 * 🎯 核心价值：单一数据源，数据驱动视图，高性能计算属性
 * 📦 功能范围：矩阵数据、配置管理、计算属性、性能监控
 * 🔄 架构设计：基于Zustand的响应式状态管理，支持持久化和计算属性缓存
 */

import { enableMapSet, produce } from 'immer';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type {
  BasicColorType,
  BusinessMode,
  CellData,
  ComputedCache,
  ContentMode,
  Coordinate,
  DataAvailability,
  MainMode,
  MatrixConfig,
  MatrixData,
  ProcessedMatrixData
} from './MatrixTypes';

// 启用 Immer 的 MapSet 插件以支持 Map 和 Set 数据结构
enableMapSet();

import {
  DEFAULT_MATRIX_CONFIG,
  MATRIX_SIZE,
  coordinateKey,
  createDefaultCell,
} from './MatrixTypes';

import {
  getCachedCompleteData,
  getMatrixDataByCoordinate,
  type MatrixDataSet
} from '../../stores/GroupAData';


import { useWordStore } from '../word/WordStore';
import { checkDataAvailability, matrixCore } from './MatrixHook';

// ===== 状态接口 =====

interface MatrixStoreState {
  // 核心数据
  data: MatrixData;
  config: MatrixConfig;

  // 完整矩阵数据（A-M组）
  matrixData: MatrixDataSet;

  // 计算属性缓存
  cache: ComputedCache;

  // 词语绑定数据
  cellWordBindings: Map<string, string>; // key: "x,y", value: wordId

  // 状态标识
  isLoading: boolean;
  isDirty: boolean;
  lastUpdate: number;
}

interface MatrixStoreActions {
  // 数据操作
  initializeMatrix: () => void;
  updateCell: (x: number, y: number, updates: Partial<CellData>) => void;
  updateCells: (updates: Array<{ x: number; y: number; data: Partial<CellData> }>) => void;
  clearMatrix: () => void;

  // 配置操作
  setMode: (mode: BusinessMode) => void;
  setMainMode: (mode: MainMode) => void;
  setContentMode: (mode: ContentMode) => void;
  setModeConfig: (mainMode: MainMode, contentMode: ContentMode) => void;
  getDataAvailability: () => DataAvailability;

  // 交互操作
  selectCell: (x: number, y: number, multiSelect?: boolean) => void;
  selectCells: (coordinates: Coordinate[]) => void;
  clearSelection: () => void;
  hoverCell: (x: number, y: number) => void;
  focusCell: (x: number, y: number) => void;

  // 计算属性
  getProcessedData: () => ProcessedMatrixData;
  getCellRenderData: (x: number, y: number) => any;

  // 矩阵数据操作
  getMatrixDataByCoordinate: (x: number, y: number) => any;
  hasMatrixData: (x: number, y: number) => boolean;

  // 调试和分析工具
  getMatrixDataStats: () => any;
  analyzeQueryPerformance: (sampleSize?: number) => any;

  // 缓存管理
  invalidateCache: () => void;
  updateCache: () => void;

  // 词语绑定操作
  bindWordToCell: (x: number, y: number, wordId: string) => void;
  unbindWordFromCell: (x: number, y: number) => void;
  getCellWord: (x: number, y: number) => string | null;
  getAllWordBindings: () => Map<string, string>;
  clearAllWordBindings: () => void;
  cleanupInvalidWordBindings: () => void;
}

type MatrixStore = MatrixStoreState & MatrixStoreActions;

// ===== 初始状态 =====

const createInitialData = (): MatrixData => ({
  cells: new Map(),
  selectedCells: new Set(),
  hoveredCell: null,
  focusedCell: null,
});

const createInitialCache = (): ComputedCache => ({
  cellStyles: new Map(),
  cellContents: new Map(),
  cellClassNames: new Map(),
  interactionStates: new Map(),
  lastUpdate: 0,
});



// ===== 辅助函数 =====

// 统一的状态更新辅助函数
const updateStateMetadata = (state: any) => {
  state.isDirty = true;
  state.lastUpdate = Date.now();
};

// 清除所有单元格选中状态的辅助函数
const clearAllCellSelections = (cells: Map<string, CellData>) => {
  cells.forEach((cell: CellData) => {
    cell.isSelected = false;
  });
};

// 更新单元格属性的辅助函数
const updateCellProperty = (cells: Map<string, CellData>, key: string, property: string, value: any) => {
  const cell = cells.get(key);
  if (cell) {
    (cell as any)[property] = value;
  }
};

// 词语查找辅助函数
const findWordText = (wordId: string): string => {
  try {
    const wordState = useWordStore.getState();

    // 遍历所有词库查找词语
    for (const library of wordState.libraries.values()) {
      const word = library.words.find((w: any) => w.id === wordId);
      if (word) {
        return word.text;
      }
    }
  } catch (error) {
    // 如果获取失败，返回空字符串
    console.warn('词语查找失败:', error);
  }

  // 如果找不到词语，返回空字符串而不是wordId
  // 这样可以避免在词库清理后显示数字ID
  return '';
};

// 初始化单元格数据的辅助函数
const initializeCellsData = (matrixData: MatrixDataSet) => {
  const cells = new Map<string, CellData>();
  let cellsWithData = 0;

  for (let x = 0; x < MATRIX_SIZE; x++) {
    for (let y = 0; y < MATRIX_SIZE; y++) {
      const key = coordinateKey(x, y);
      const cell = createDefaultCell(x, y);

      // 直接从预建索引中查找数据点（高性能）
      const matrixDataPoint = matrixData.byCoordinate.get(key);

      if (matrixDataPoint) {
        cellsWithData++;
        // 设置颜色（包括黑色，因为CellData.color支持所有BasicColorType）
        cell.color = matrixDataPoint.color as BasicColorType;
        cell.level = matrixDataPoint.level;
        cell.value = matrixDataPoint.level; // 设置数值为层级
      }

      cells.set(key, cell);
    }
  }

  return { cells, cellsWithData };
};

// 通用配置更新函数
const updateConfig = (state: any, updates: Partial<MatrixConfig>) => {
  Object.assign(state.config, updates);
  updateStateMetadata(state);
};

// ===== Store实现 =====

export const useMatrixStore = create<MatrixStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      data: createInitialData(),
      config: DEFAULT_MATRIX_CONFIG,
      matrixData: getCachedCompleteData(),
      cache: createInitialCache(),
      cellWordBindings: new Map(),
      isLoading: false,
      isDirty: false,
      lastUpdate: Date.now(),

      // 数据操作
      initializeMatrix: () => {
        set(produce((state) => {
          state.isLoading = true;
          state.data.cells.clear();

          const totalDataPoints = state.matrixData.metadata.totalPoints;
          console.log(`开始初始化矩阵 - 数据点总数: ${totalDataPoints}, 网格大小: ${MATRIX_SIZE}x${MATRIX_SIZE}`);

          // 使用辅助函数初始化单元格数据
          const { cells, cellsWithData } = initializeCellsData(state.matrixData);

          // 批量设置到状态中
          state.data.cells = cells;
          updateStateMetadata(state);
          state.isLoading = false;

          console.log(`矩阵初始化完成 - 处理单元格: ${MATRIX_SIZE * MATRIX_SIZE}, 包含数据的单元格: ${cellsWithData}`);
        }));

        console.log(`✅ 矩阵初始化完成`);
      },

      updateCell: (x: number, y: number, updates: Partial<CellData>) => {
        set(produce((state) => {
          const key = coordinateKey(x, y);
          const cell = state.data.cells.get(key);

          if (cell) {
            Object.assign(cell, updates);
            updateStateMetadata(state);
          }
        }));

        get().invalidateCache();
      },

      updateCells: (updates) => {
        set(produce((state) => {
          updates.forEach(({ x, y, data }) => {
            const key = coordinateKey(x, y);
            const cell = state.data.cells.get(key);

            if (cell) {
              Object.assign(cell, data);
            }
          });

          updateStateMetadata(state);
        }));

        get().invalidateCache();
      },

      clearMatrix: () => {
        set(produce((state) => {
          state.data.cells.clear();
          state.data.selectedCells.clear();
          state.data.hoveredCell = null;
          state.data.focusedCell = null;
          updateStateMetadata(state);
        }));

        get().invalidateCache();
      },

      // 配置操作
      setMode: (mode: BusinessMode) => {
        set(produce((state) => {
          updateConfig(state, { mode });
        }));
        get().invalidateCache();
      },

      setMainMode: (mode: MainMode) => {
        set(produce((state) => {
          updateConfig(state, { mainMode: mode });
        }));
        get().invalidateCache();
      },

      setContentMode: (mode: ContentMode) => {
        set(produce((state) => {
          updateConfig(state, { contentMode: mode });
        }));
        get().invalidateCache();
      },

      setModeConfig: (mainMode: MainMode, contentMode: ContentMode) => {
        set(produce((state) => {
          updateConfig(state, { mainMode, contentMode });
        }));
        get().invalidateCache();
      },

      getDataAvailability: () => {
        return checkDataAvailability();
      },



      // 交互操作
      selectCell: (x: number, y: number, multiSelect = false) => {
        set(produce((state) => {
          const key = coordinateKey(x, y);

          if (!multiSelect) {
            state.data.selectedCells.clear();
            clearAllCellSelections(state.data.cells);
          }

          state.data.selectedCells.add(key);
          updateCellProperty(state.data.cells, key, 'isSelected', true);
          state.lastUpdate = Date.now();
        }));
      },

      selectCells: (coordinates: Coordinate[]) => {
        set(produce((state) => {
          state.data.selectedCells.clear();
          clearAllCellSelections(state.data.cells);

          // 设置新的选中状态
          coordinates.forEach(({ x, y }) => {
            const key = coordinateKey(x, y);
            state.data.selectedCells.add(key);
            updateCellProperty(state.data.cells, key, 'isSelected', true);
          });

          state.lastUpdate = Date.now();
        }));
      },

      clearSelection: () => {
        set(produce((state) => {
          state.data.selectedCells.clear();
          clearAllCellSelections(state.data.cells);
          state.lastUpdate = Date.now();
        }));
      },

      hoverCell: (x: number, y: number) => {
        set(produce((state) => {
          // 清除之前的悬停状态
          if (state.data.hoveredCell) {
            updateCellProperty(state.data.cells, state.data.hoveredCell, 'isHovered', false);
          }

          // 设置新的悬停状态
          const key = coordinateKey(x, y);
          state.data.hoveredCell = key;
          updateCellProperty(state.data.cells, key, 'isHovered', true);
          state.lastUpdate = Date.now();
        }));
      },

      focusCell: (x: number, y: number) => {
        set(produce((state) => {
          const key = coordinateKey(x, y);
          state.data.focusedCell = key;
          state.lastUpdate = Date.now();
        }));
      },

      // 计算属性
      getProcessedData: (): ProcessedMatrixData => {
        const state = get();
        // 使用矩阵核心引擎处理数据
        return matrixCore.processData(state.data, state.config);
      },

      getCellRenderData: (x: number, y: number) => {
        const state = get();
        const key = coordinateKey(x, y);
        const cell = state.data.cells.get(key);

        if (!cell) return null;

        // 创建包含词语绑定信息的单元格副本
        const cellWithWord = { ...cell };
        const wordId = state.cellWordBindings.get(key);
        if (wordId) {
          cellWithWord.word = findWordText(wordId);
        }

        // 使用MatrixCore统一的业务逻辑
        return matrixCore.renderCell(cellWithWord, state.config);
      },

      // 缓存管理
      invalidateCache: () => {
        set(produce((state) => {
          state.cache.cellStyles.clear();
          state.cache.cellContents.clear();
          state.cache.cellClassNames.clear();
          state.cache.interactionStates.clear();
          state.cache.lastUpdate = Date.now();
        }));
      },

      updateCache: () => {
        const state = get();
        const processedData = state.getProcessedData();

        set(produce((draft) => {
          draft.cache.cellStyles.clear();
          draft.cache.cellContents.clear();
          draft.cache.cellClassNames.clear();

          processedData.renderData.forEach((renderData, key) => {
            draft.cache.cellStyles.set(key, renderData.style);
            draft.cache.cellContents.set(key, renderData.content);
            draft.cache.cellClassNames.set(key, renderData.className);
          });

          draft.cache.lastUpdate = Date.now();
        }));
      },

      // 矩阵数据操作
      getMatrixDataByCoordinate: (x: number, y: number) => {
        return getMatrixDataByCoordinate(get().matrixData, x, y);
      },

      hasMatrixData: (x: number, y: number) => {
        const key = coordinateKey(x, y);
        return get().matrixData.byCoordinate.has(key);
      },

      // 调试和分析工具
      getMatrixDataStats: () => {
        const state = get();
        const { metadata, byCoordinate } = state.matrixData;

        // 分析数据覆盖范围
        let minX = MATRIX_SIZE, maxX = -1, minY = MATRIX_SIZE, maxY = -1;
        const coordinateRanges: Record<string, { count: number; coordinates: string[] }> = {};

        byCoordinate.forEach((_, key) => {
          const [x, y] = key.split(',').map(Number);
          minX = Math.min(minX, x);
          maxX = Math.max(maxX, x);
          minY = Math.min(minY, y);
          maxY = Math.max(maxY, y);

          // 按行分组统计
          const rowKey = `row_${y}`;
          if (!coordinateRanges[rowKey]) {
            coordinateRanges[rowKey] = { count: 0, coordinates: [] };
          }
          coordinateRanges[rowKey].count++;
          coordinateRanges[rowKey].coordinates.push(key);
        });

        return {
          totalDataPoints: metadata.totalPoints,
          coverageRange: {
            x: { min: minX, max: maxX, span: maxX - minX + 1 },
            y: { min: minY, max: maxY, span: maxY - minY + 1 }
          },
          colorDistribution: metadata.colorCounts,
          levelDistribution: metadata.levelCounts,
          groupDistribution: metadata.groupCounts,
          rowCoverage: coordinateRanges,
          coveragePercentage: ((metadata.totalPoints / (MATRIX_SIZE * MATRIX_SIZE)) * 100).toFixed(2) + '%'
        };
      },

      // 性能分析工具
      analyzeQueryPerformance: (sampleSize: number = 100) => {
        const state = get();
        const startTime = performance.now();

        // 随机采样查询
        const results = {
          totalQueries: sampleSize,
          successfulQueries: 0,
          failedQueries: 0,
          averageQueryTime: 0,
          queryTimes: [] as number[]
        };

        for (let i = 0; i < sampleSize; i++) {
          const x = Math.floor(Math.random() * MATRIX_SIZE);
          const y = Math.floor(Math.random() * MATRIX_SIZE);

          const queryStart = performance.now();
          const result = getMatrixDataByCoordinate(state.matrixData, x, y);
          const queryEnd = performance.now();

          const queryTime = queryEnd - queryStart;
          results.queryTimes.push(queryTime);

          if (result) {
            results.successfulQueries++;
          } else {
            results.failedQueries++;
          }
        }

        const totalTime = performance.now() - startTime;
        results.averageQueryTime = results.queryTimes.reduce((a, b) => a + b, 0) / sampleSize;

        console.log('🔍 查询性能分析结果:', {
          ...results,
          totalAnalysisTime: `${totalTime.toFixed(2)}ms`,
          successRate: `${((results.successfulQueries / sampleSize) * 100).toFixed(1)}%`
        });

        return results;
      },

      // ===== 词语绑定操作 =====

      bindWordToCell: (x: number, y: number, wordId: string) => {
        set(produce((state) => {
          const key = coordinateKey(x, y);
          state.cellWordBindings.set(key, wordId);
          updateCellProperty(state.data.cells, key, 'word', wordId);
          updateStateMetadata(state);
        }));
      },

      unbindWordFromCell: (x: number, y: number) => {
        set(produce((state) => {
          const key = coordinateKey(x, y);
          state.cellWordBindings.delete(key);
          updateCellProperty(state.data.cells, key, 'word', undefined);
          updateStateMetadata(state);
        }));
      },

      getCellWord: (x: number, y: number) => {
        const key = coordinateKey(x, y);
        return get().cellWordBindings.get(key) || null;
      },

      getAllWordBindings: () => {
        return new Map(get().cellWordBindings);
      },

      clearAllWordBindings: () => {
        set(produce((state) => {
          state.cellWordBindings.clear();

          // 清除所有单元格的word属性
          state.data.cells.forEach((cell: CellData) => {
            cell.word = undefined;
          });

          updateStateMetadata(state);
        }));
      },

      /** 清理无效的词语绑定（词库中已不存在的词语） */
      cleanupInvalidWordBindings: () => {
        const state = get();
        const wordState = useWordStore.getState();
        const validWordIds = new Set<string>();

        // 收集所有有效的词语ID
        wordState.libraries.forEach((library) => {
          library.words.forEach((word) => {
            validWordIds.add(word.id);
          });
        });

        // 清理无效的绑定
        const invalidBindings: string[] = [];
        state.cellWordBindings.forEach((wordId, cellKey) => {
          if (!validWordIds.has(wordId)) {
            invalidBindings.push(cellKey);
          }
        });

        if (invalidBindings.length > 0) {
          set(produce((state) => {
            invalidBindings.forEach((cellKey) => {
              state.cellWordBindings.delete(cellKey);

              // 清除对应单元格的word属性
              const cell = state.data.cells.get(cellKey);
              if (cell) {
                cell.word = undefined;
              }
            });

            updateStateMetadata(state);
          }));

          console.log(`🧹 清理了 ${invalidBindings.length} 个无效的词语绑定`);
        }
      },

    }),
    {
      name: 'matrix-store',
      version: 1,
      // 只持久化配置和基础数据，不持久化缓存
      partialize: (state) => ({
        config: state.config,
        data: {
          ...state.data,
          cells: Array.from(state.data.cells.entries()),
          selectedCells: Array.from(state.data.selectedCells),
        },
        cellWordBindings: Array.from(state.cellWordBindings.entries()),
      }),
      // 反序列化时重建Map和Set
      onRehydrateStorage: () => (state) => {
        if (state?.data) {
          state.data.cells = new Map(state.data.cells as any);
          state.data.selectedCells = new Set(state.data.selectedCells as any);
        }
        if (state?.cellWordBindings) {
          state.cellWordBindings = new Map(state.cellWordBindings as any);
        }
      },
    }
  )
);

// ===== 选择器钩子 =====

export const useMatrixData = () => useMatrixStore((state) => state.data);
export const useMatrixConfig = () => useMatrixStore((state) => state.config);
export const useMatrixMode = () => useMatrixStore((state) => state.config.mode);
export const useSelectedCells = () => useMatrixStore((state) => state.data.selectedCells);
export const useHoveredCell = () => useMatrixStore((state) => state.data.hoveredCell);
export const useFocusedCell = () => useMatrixStore((state) => state.data.focusedCell);
export const useIsMatrixDirty = () => useMatrixStore((state) => state.isDirty);
