/**
 * 词库UI优化功能测试脚本
 * 🎯 测试目标：验证词库UI的6项优化功能
 * 📦 测试范围：hover删除、双击编辑、换行显示、滚动、简化名称、统一样式
 * 🔄 使用方法：在开发环境中运行此脚本进行功能验证
 */

import React, { useState } from 'react';
import WordInput from '@/features/word/WordInputUI';
import WordManager from '@/features/word/WordContainer';
import type { BasicColorType, DataLevel } from '@/features/matrix/MatrixTypes';

const WordUIOptimizationTest: React.FC = () => {
  const [testMode, setTestMode] = useState<'single' | 'manager'>('single');

  return (
    <div className="p-8 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-8 text-gray-800">词库UI优化功能测试</h1>
      
      {/* 测试模式切换 */}
      <div className="mb-8">
        <div className="flex gap-4">
          <button
            onClick={() => setTestMode('single')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              testMode === 'single' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            单个词库测试
          </button>
          <button
            onClick={() => setTestMode('manager')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              testMode === 'manager' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            词库管理器测试
          </button>
        </div>
      </div>

      {/* 功能说明 */}
      <div className="mb-8 p-6 bg-blue-50 rounded-lg">
        <h2 className="text-xl font-semibold mb-4 text-blue-800">测试功能说明</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span><strong>hover显示删除按钮：</strong>悬停词语时显示"×"按钮</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span><strong>双击编辑词语：</strong>双击词语进入编辑模式</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span><strong>词语换行显示：</strong>超过宽度时自动换行</span>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span><strong>面板滚动功能：</strong>词库列表支持滚动查看</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span><strong>简化名称：</strong>去掉"色"字，如"黑1"而非"黑色1级"</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span><strong>统一样式：</strong>圆角、间距、颜色等样式统一</span>
            </div>
          </div>
        </div>
      </div>

      {/* 测试内容 */}
      {testMode === 'single' ? (
        <div className="space-y-8">
          <h2 className="text-2xl font-semibold text-gray-800">单个词库测试</h2>
          
          {/* 测试不同颜色和级别的词库 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-medium mb-4 text-gray-700">黑色词库测试</h3>
              <WordInput
                libraryKey="black-1"
                color="black"
                level={1}
                collapsed={false}
                placeholder="测试黑色词库功能..."
                className="mb-4"
              />
              <WordInput
                libraryKey="black-2"
                color="black"
                level={2}
                collapsed={false}
                placeholder="测试黑色2级词库..."
              />
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-4 text-gray-700">彩色词库测试</h3>
              <WordInput
                libraryKey="red-1"
                color="red"
                level={1}
                collapsed={false}
                placeholder="测试红色词库功能..."
                className="mb-4"
              />
              <WordInput
                libraryKey="blue-1"
                color="blue"
                level={1}
                collapsed={false}
                placeholder="测试蓝色词库功能..."
              />
            </div>
          </div>

          {/* 测试说明 */}
          <div className="mt-8 p-4 bg-yellow-50 rounded-lg">
            <h4 className="font-medium text-yellow-800 mb-2">测试步骤：</h4>
            <ol className="list-decimal list-inside space-y-1 text-sm text-yellow-700">
              <li>在输入框中输入一些词语（用逗号分隔）</li>
              <li>点击展开按钮（+/-）测试折叠功能</li>
              <li>悬停在词语上查看删除按钮是否出现</li>
              <li>双击词语测试编辑功能</li>
              <li>输入长词语测试换行显示</li>
              <li>观察样式是否统一（圆角、间距等）</li>
            </ol>
          </div>
        </div>
      ) : (
        <div className="space-y-8">
          <h2 className="text-2xl font-semibold text-gray-800">词库管理器测试</h2>
          
          {/* 词库管理器 */}
          <div className="border rounded-lg p-4" style={{ height: '600px' }}>
            <WordManager 
              className="h-full"
              isColorWordMode={true}
            />
          </div>

          {/* 测试说明 */}
          <div className="mt-8 p-4 bg-yellow-50 rounded-lg">
            <h4 className="font-medium text-yellow-800 mb-2">测试步骤：</h4>
            <ol className="list-decimal list-inside space-y-1 text-sm text-yellow-700">
              <li>滚动词库列表，测试滚动功能是否流畅</li>
              <li>观察词库名称是否已简化（如"黑1"而非"黑色1级"）</li>
              <li>在不同词库中添加词语，测试各项功能</li>
              <li>测试hover删除和双击编辑功能</li>
              <li>观察整体样式是否统一美观</li>
              <li>测试词库的展开/折叠功能</li>
            </ol>
          </div>
        </div>
      )}

      {/* 版本信息 */}
      <div className="mt-12 pt-8 border-t border-gray-200">
        <div className="text-sm text-gray-500">
          <p><strong>优化版本：</strong>v2.0</p>
          <p><strong>更新时间：</strong>{new Date().toLocaleDateString()}</p>
          <p><strong>优化内容：</strong>hover删除、双击编辑、换行显示、滚动优化、名称简化、样式统一</p>
        </div>
      </div>
    </div>
  );
};

export default WordUIOptimizationTest;
