{"compilerOptions": {"target": "es2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "downlevelIteration": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"], "@/components/*": ["./components/*"], "@/lib/*": ["./lib/*"], "@/utils/*": ["./lib/utils/*"], "@/types/*": ["./lib/types/*"], "@/stores/*": ["./stores/*"], "@/features/*": ["./features/*"], "@/api/*": ["./app/api/*"], "@/app/*": ["./app/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}