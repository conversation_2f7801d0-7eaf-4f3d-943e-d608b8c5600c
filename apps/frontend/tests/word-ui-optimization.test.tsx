/**
 * 词库UI优化功能单元测试
 * 🎯 测试目标：验证词库UI的6项优化功能
 * 📦 测试范围：hover删除、双击编辑、换行显示、滚动、简化名称、统一样式
 * 🔄 运行方法：npm test word-ui-optimization.test.tsx
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

import WordInput from '@/features/word/WordInputUI';
import WordManager from '@/features/word/WordContainer';
import { getWordLibraryDisplayName, getWordLibraryShortName } from '@/features/word/WordConfig';

// Mock Toast组件
jest.mock('@/components/ui/Toast', () => ({
  toast: {
    controlPanel: {
      success: jest.fn(),
      error: jest.fn(),
      warning: jest.fn()
    }
  }
}));

// Mock WordStore
const mockWordStore = {
  getLibrary: jest.fn(),
  validateInput: jest.fn(),
  addWord: jest.fn(),
  removeWord: jest.fn(),
  updateWord: jest.fn(),
  toggleLibraryCollapse: jest.fn(),
  checkCrossLibraryDuplicate: jest.fn(),
  getWordHighlightColor: jest.fn(),
  resetAllLibraries: jest.fn(),
  exportData: jest.fn(),
  importData: jest.fn()
};

const mockWordInputStore = {
  temporaryWord: null,
  isActive: false,
  matchedLibrary: null
};

jest.mock('@/features/word/WordStore', () => ({
  useWordStore: () => mockWordStore,
  useWordInputStore: () => mockWordInputStore
}));

describe('词库UI优化功能测试', () => {
  beforeEach(() => {
    // 重置所有mock
    jest.clearAllMocks();
    
    // 设置默认的mock返回值
    mockWordStore.getLibrary.mockReturnValue({
      key: 'black-1',
      color: 'black',
      level: 1,
      words: [
        {
          id: 'word1',
          text: '测试词语',
          color: 'black',
          level: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
          usageCount: 0,
          usagePositions: []
        }
      ],
      collapsed: false,
      lastUpdated: new Date()
    });
    
    mockWordStore.validateInput.mockReturnValue({ isValid: true, errors: [] });
    mockWordStore.addWord.mockReturnValue({ isValid: true, errors: [], isDuplicate: false, duplicateLibraries: [] });
    mockWordStore.checkCrossLibraryDuplicate.mockReturnValue({ isDuplicate: false, duplicateLibraries: [] });
  });

  describe('1. hover显示删除按钮功能', () => {
    test('应该在hover时显示删除按钮', async () => {
      const user = userEvent.setup();
      
      render(
        <WordInput
          libraryKey="black-1"
          color="black"
          level={1}
          collapsed={false}
        />
      );

      // 查找词语元素
      const wordElement = screen.getByText(/测试词语/);
      expect(wordElement).toBeInTheDocument();

      // 悬停在词语上
      await user.hover(wordElement.closest('.group'));

      // 检查删除按钮是否可见
      const deleteButton = screen.getByText('×');
      expect(deleteButton).toBeInTheDocument();
      expect(deleteButton).toHaveClass('group-hover:opacity-100');
    });

    test('应该在点击删除按钮时调用removeWord', async () => {
      const user = userEvent.setup();
      
      render(
        <WordInput
          libraryKey="black-1"
          color="black"
          level={1}
          collapsed={false}
        />
      );

      // 悬停并点击删除按钮
      const wordElement = screen.getByText(/测试词语/);
      await user.hover(wordElement.closest('.group'));
      
      const deleteButton = screen.getByText('×');
      await user.click(deleteButton);

      expect(mockWordStore.removeWord).toHaveBeenCalledWith('black-1', 'word1');
    });
  });

  describe('2. 双击编辑词语功能', () => {
    test('应该在双击词语时进入编辑模式', async () => {
      const user = userEvent.setup();
      
      render(
        <WordInput
          libraryKey="black-1"
          color="black"
          level={1}
          collapsed={false}
        />
      );

      // 双击词语
      const wordElement = screen.getByText(/测试词语/);
      await user.dblClick(wordElement);

      // 检查是否出现编辑输入框
      const editInput = screen.getByDisplayValue('测试词语');
      expect(editInput).toBeInTheDocument();
      expect(editInput).toHaveClass('bg-white');
    });

    test('应该在按Enter时保存编辑', async () => {
      const user = userEvent.setup();
      mockWordStore.updateWord.mockReturnValue({ isValid: true, errors: [], isDuplicate: false, duplicateLibraries: [] });
      
      render(
        <WordInput
          libraryKey="black-1"
          color="black"
          level={1}
          collapsed={false}
        />
      );

      // 双击进入编辑模式
      const wordElement = screen.getByText(/测试词语/);
      await user.dblClick(wordElement);

      // 修改文本并按Enter
      const editInput = screen.getByDisplayValue('测试词语');
      await user.clear(editInput);
      await user.type(editInput, '新词语');
      await user.keyboard('{Enter}');

      expect(mockWordStore.updateWord).toHaveBeenCalledWith('black-1', 'word1', '新词语');
    });
  });

  describe('3. 词语换行显示功能', () => {
    test('应该使用flex-wrap类实现换行', () => {
      render(
        <WordInput
          libraryKey="black-1"
          color="black"
          level={1}
          collapsed={false}
        />
      );

      // 检查词语容器是否有flex-wrap类
      const wordContainer = document.querySelector('.flex-wrap');
      expect(wordContainer).toBeInTheDocument();
    });
  });

  describe('4. 简化词库名称功能', () => {
    test('应该返回简化的颜色名称', () => {
      // 测试显示名称
      expect(getWordLibraryDisplayName('black', 1)).toBe('黑1级');
      expect(getWordLibraryDisplayName('red', 2)).toBe('红2级');
      
      // 测试简化名称
      expect(getWordLibraryShortName('black', 1)).toBe('黑1');
      expect(getWordLibraryShortName('blue', 3)).toBe('蓝3');
    });

    test('应该在UI中显示简化名称', () => {
      render(
        <WordInput
          libraryKey="black-1"
          color="black"
          level={1}
          collapsed={false}
        />
      );

      // 检查是否显示简化名称
      expect(screen.getByText(/黑1\[/)).toBeInTheDocument();
    });
  });

  describe('5. 统一样式优化', () => {
    test('应该使用统一的圆角样式', () => {
      render(
        <WordInput
          libraryKey="black-1"
          color="black"
          level={1}
          collapsed={false}
        />
      );

      // 检查主容器圆角
      const container = document.querySelector('.rounded-lg');
      expect(container).toBeInTheDocument();
    });

    test('应该使用统一的间距', () => {
      render(
        <WordInput
          libraryKey="black-1"
          color="black"
          level={1}
          collapsed={false}
        />
      );

      // 检查内边距
      const mainContainer = document.querySelector('.p-4');
      expect(mainContainer).toBeInTheDocument();

      // 检查元素间距
      const gapContainer = document.querySelector('.gap-3');
      expect(gapContainer).toBeInTheDocument();
    });
  });

  describe('6. 词库管理器滚动功能', () => {
    test('应该渲染词库管理器组件', () => {
      render(<WordManager isColorWordMode={true} />);
      
      // 检查标题是否存在
      expect(screen.getByText('词库管理')).toBeInTheDocument();
    });

    test('应该有滚动相关的CSS类', () => {
      render(<WordManager isColorWordMode={true} />);
      
      // 检查滚动容器
      const scrollContainer = document.querySelector('.overflow-y-auto');
      expect(scrollContainer).toBeInTheDocument();
    });
  });
});

describe('集成测试', () => {
  test('应该能够完整地添加、编辑和删除词语', async () => {
    const user = userEvent.setup();
    
    render(
      <WordInput
        libraryKey="black-1"
        color="black"
        level={1}
        collapsed={false}
      />
    );

    // 1. 添加词语
    const input = screen.getByPlaceholderText(/输入词语/);
    await user.type(input, '新词语，');
    
    expect(mockWordStore.addWord).toHaveBeenCalled();

    // 2. 双击编辑（模拟词语已添加）
    const wordElement = screen.getByText(/测试词语/);
    await user.dblClick(wordElement);
    
    const editInput = screen.getByDisplayValue('测试词语');
    await user.clear(editInput);
    await user.type(editInput, '编辑后的词语');
    await user.keyboard('{Enter}');

    expect(mockWordStore.updateWord).toHaveBeenCalled();

    // 3. hover删除
    await user.hover(wordElement.closest('.group'));
    const deleteButton = screen.getByText('×');
    await user.click(deleteButton);

    expect(mockWordStore.removeWord).toHaveBeenCalled();
  });
});
