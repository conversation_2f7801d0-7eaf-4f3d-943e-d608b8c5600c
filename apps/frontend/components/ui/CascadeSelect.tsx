/**
 * 联动选择器组件
 * 🎯 核心价值：统一的格子内容和背景色管理，两级联动选择
 * 📦 功能范围：主模式和内容模式的联动选择，数据驱动的选项启用/禁用
 * 🔄 架构设计：基于数据可用性的智能选项管理
 */

'use client';

import type { ContentMode, DataAvailability, MainMode } from '@/features/matrix/MatrixTypes';
import React, { memo, useCallback, useMemo } from 'react';
import Select, { type SelectOption } from './Select';

// ===== 组件属性 =====

interface CascadeSelectProps {
  /** 当前主模式 */
  mainMode: MainMode;
  /** 当前内容模式 */
  contentMode: ContentMode;
  /** 模式变化回调 */
  onModeChange: (mainMode: MainMode, contentMode: ContentMode) => void;
  /** 数据可用性 */
  dataAvailability: DataAvailability;
  /** 自定义类名 */
  className?: string;
  /** 是否禁用 */
  disabled?: boolean;
}

// ===== 选项配置 =====

const MAIN_MODE_OPTIONS: SelectOption[] = [
  { value: 'default', label: '默认', description: '白色背景，基础显示模式' },
  { value: 'color', label: '颜色', description: '数据色背景，彩色显示模式' },
];

// ===== 主组件 =====

const CascadeSelectComponent: React.FC<CascadeSelectProps> = ({
  mainMode,
  contentMode,
  onModeChange,
  dataAvailability,
  className = '',
  disabled = false,
}) => {
  // 根据主模式和数据可用性生成内容模式选项
  const contentModeOptions = useMemo((): SelectOption[] => {
    const baseOptions: SelectOption[] = [
      { value: 'blank', label: '空白', description: '不显示任何内容' },
      { value: 'index', label: '索引', description: '显示格子坐标索引' },
      {
        value: 'coordinate',
        label: '坐标',
        description: '显示转换后的坐标',
        disabled: !dataAvailability.hasCoordinateData
      },
    ];

    // 颜色模式下添加额外选项
    if (mainMode === 'color') {
      baseOptions.push(
        {
          value: 'level',
          label: '等级',
          description: '显示数据等级',
          disabled: !dataAvailability.hasLevelData
        },
        {
          value: 'mapping',
          label: '映射',
          description: '显示映射值',
          disabled: !dataAvailability.hasMappingData
        },
        {
          value: 'word',
          label: '词语',
          description: '显示词语内容',
          disabled: !dataAvailability.hasWordData
        }
      );
    }

    return baseOptions;
  }, [mainMode, dataAvailability]);

  // 处理主模式变化
  const handleMainModeChange = useCallback((newMainMode: string) => {
    const mainModeValue = newMainMode as MainMode;

    // 如果切换到默认模式，且当前内容模式不支持，则重置为空白
    if (mainModeValue === 'default' && !['blank', 'index', 'coordinate'].includes(contentMode)) {
      onModeChange(mainModeValue, 'blank');
    } else {
      onModeChange(mainModeValue, contentMode);
    }
  }, [contentMode, onModeChange]);

  // 处理内容模式变化
  const handleContentModeChange = useCallback((newContentMode: string) => {
    onModeChange(mainMode, newContentMode as ContentMode);
  }, [mainMode, onModeChange]);

  // 检查当前内容模式是否在可用选项中
  const isCurrentContentModeValid = useMemo(() => {
    return contentModeOptions.some(option => option.value === contentMode && !option.disabled);
  }, [contentModeOptions, contentMode]);

  // 如果当前内容模式无效，自动选择第一个可用选项
  React.useEffect(() => {
    if (!isCurrentContentModeValid) {
      const firstAvailableOption = contentModeOptions.find(option => !option.disabled);
      if (firstAvailableOption) {
        onModeChange(mainMode, firstAvailableOption.value as ContentMode);
      }
    }
  }, [isCurrentContentModeValid, contentModeOptions, mainMode, onModeChange]);

  return (
    <div className={`cascade-select space-y-3 ${className}`}>
      {/* 主模式选择器 */}
      <div>
        {/* <label className="block text-sm font-medium text-gray-700 mb-1">
          显示模式
        </label> */}
        <Select
          value={mainMode}
          options={MAIN_MODE_OPTIONS}
          onChange={handleMainModeChange}
          disabled={disabled}
          placeholder="选择显示模式"
        />
      </div>

      {/* 内容模式选择器 */}
      <div>
        {/* <label className="block text-sm font-medium text-gray-700 mb-1">
          内容类型
        </label> */}
        <Select
          value={contentMode}
          options={contentModeOptions}
          onChange={handleContentModeChange}
          disabled={disabled}
          placeholder="选择内容类型"
        />
      </div>

      {/* 模式说明 */}
      {/* <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
        <div className="font-medium mb-1">当前配置：</div>
        <div>
          {mainMode === 'default' ? '默认白色背景' : '数据色彩背景'} + {' '}
          {contentModeOptions.find(opt => opt.value === contentMode)?.label || '未知'}内容
        </div>
      </div> */}
    </div>
  );
};

// ===== 性能优化 =====

const CascadeSelect = memo(CascadeSelectComponent);

CascadeSelect.displayName = 'CascadeSelect';

export default CascadeSelect;

// ===== 导出类型 =====

export type { CascadeSelectProps };
