/**
 * 矩阵主组件
 * 🎯 核心价值：纯渲染组件，数据驱动视图，零业务逻辑
 * 📦 功能范围：矩阵渲染、交互事件传递、性能优化
 * 🔄 架构设计：完全无状态组件，所有逻辑通过props注入
 */

'use client';

import { getCellStyle, getContainerStyle, getViewportStyle } from '@/features/matrix/MatrixConfig';
import { matrixCore } from '@/features/matrix/MatrixHook';
import { useMatrixConfig, useMatrixData, useMatrixStore } from '@/features/matrix/MatrixStore';
import type {
  BusinessMode,
  Coordinate,
  MatrixConfig,
} from '@/features/matrix/MatrixTypes';
import { MATRIX_SIZE } from '@/features/matrix/MatrixTypes';
import { useWordInputStore } from '@/features/word/WordStore';
import React, { memo, useCallback, useEffect, useRef, useState } from 'react';

import { createInteractionEvent } from '@/features/matrix/MatrixHook';

// ===== 组件属性 =====

interface MatrixProps {
  /** 自定义配置覆盖 */
  configOverride?: Partial<MatrixConfig>;

  /** 容器样式 */
  className?: string;
  style?: React.CSSProperties;

  /** 交互事件回调 */
  onCellClick?: (coordinate: Coordinate, event: React.MouseEvent) => void;
  onCellDoubleClick?: (coordinate: Coordinate, event: React.MouseEvent) => void;
  onCellHover?: (coordinate: Coordinate, event: React.MouseEvent) => void;
  onCellFocus?: (coordinate: Coordinate, event: React.FocusEvent) => void;
  onModeChange?: (mode: BusinessMode) => void;
}

// ===== 主组件 =====

const MatrixComponent: React.FC<MatrixProps> = ({
  configOverride,
  className = '',
  style,
  onCellClick,
  onCellDoubleClick,
  onCellHover,
  onCellFocus,
  onModeChange,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const isInitialized = useRef(false);
  const [isClient, setIsClient] = useState(false);

  // 获取状态
  const matrixData = useMatrixData();
  const matrixConfig = useMatrixConfig();
  const {
    initializeMatrix,
    selectCell,
    // hoverCell, // 已禁用：防止触发词库滑动
    focusCell,
    getCellRenderData,
  } = useMatrixStore();

  // 获取填词模式状态
  const { isActive: isWordInputActive, selectedCell, temporaryWord } = useWordInputStore();

  // 合并配置
  const finalConfig = { ...matrixConfig, ...configOverride };

  // 简化的模式检查
  const isColorMode = finalConfig.mainMode === 'color';

  // 确保客户端渲染一致性
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 初始化
  useEffect(() => {
    if (!isInitialized.current && containerRef.current && isClient) {
      // 初始化矩阵数据
      if (matrixData.cells.size === 0) {
        initializeMatrix();
      }
      isInitialized.current = true;
    }
  }, [initializeMatrix, matrixData.cells.size, isClient]);

  // 渲染矩阵单元格
  const renderMatrixCells = () => {
    const cells = [];
    for (let y = 0; y < MATRIX_SIZE; y++) {
      for (let x = 0; x < MATRIX_SIZE; x++) {
        const cellRenderData = getCellRenderData(x, y);
        const key = `${x},${y}`;

        // 获取单元格数据以检测1级坐标
        const cell = matrixData.cells.get(key);
        const isLevel1 = cell?.level === 1;
        const isEnhanced = isColorMode && isLevel1;

        // 检测是否为填词模式下的选中单元格
        const isWordInputCell = isWordInputActive &&
          selectedCell &&
          selectedCell.x === x &&
          selectedCell.y === y;

        // 使用统一的样式计算函数
        const cellStyle = getCellStyle(isEnhanced, { x, y });

        // 构建CSS类名
        let cellClassName = cellRenderData?.className || 'matrix-cell';
        if (isWordInputCell) {
          cellClassName += ' word-input-active';
        }

        // 确定显示内容：临时词语优先于原内容（仅在【颜色】【词语】模式下）
        let displayContent = cellRenderData?.content || '';
        if (isWordInputCell && temporaryWord && matrixConfig.mainMode === 'color' && matrixConfig.contentMode === 'word') {
          displayContent = temporaryWord;
        }

        cells.push(
          <div
            key={key}
            data-x={x}
            data-y={y}
            className={cellClassName}
            style={{
              ...cellStyle,
              backgroundColor: cellRenderData?.style?.backgroundColor || '#ffffff',
              color: cellRenderData?.style?.color || '#000000',
              ...cellRenderData?.style, // 动态样式先应用
              // 确保增强样式不被覆盖
              ...(isEnhanced && {
                borderRadius: '50%',
                zIndex: 10,
              }),
            }}
          // onMouseEnter={handleCellMouseEnter} // 已禁用：防止触发词库滑动
          >
            {displayContent}
          </div>
        );
      }
    }
    return cells;
  };

  // 处理单元格点击
  const handleCellClick = useCallback((event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    const x = parseInt(target.dataset.x || '0', 10);
    const y = parseInt(target.dataset.y || '0', 10);

    if (!isNaN(x) && !isNaN(y)) {
      const coordinate = { x, y };

      // 更新状态
      selectCell(x, y, event.ctrlKey || event.metaKey);

      // 创建交互事件
      const interactionEvent = createInteractionEvent('click', coordinate, {
        ctrl: event.ctrlKey,
        shift: event.shiftKey,
        alt: event.altKey,
      });

      // 处理业务逻辑
      const cell = matrixData.cells.get(`${x},${y}`);
      if (cell) {
        matrixCore.handleInteraction(interactionEvent, cell, finalConfig);
      }

      // 调用外部回调
      onCellClick?.(coordinate, event);
    }
  }, [matrixData.cells, finalConfig, selectCell, onCellClick]);

  // 处理双击
  const handleCellDoubleClick = useCallback((event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    const x = parseInt(target.dataset.x || '0', 10);
    const y = parseInt(target.dataset.y || '0', 10);

    if (!isNaN(x) && !isNaN(y)) {
      const coordinate = { x, y };
      onCellDoubleClick?.(coordinate, event);
    }
  }, [onCellDoubleClick]);

  // 处理悬停 - 已禁用：防止触发词库滑动
  // const handleCellMouseEnter = useCallback((event: React.MouseEvent) => {
  //   const target = event.target as HTMLElement;
  //   const x = parseInt(target.dataset.x || '0', 10);
  //   const y = parseInt(target.dataset.y || '0', 10);

  //   if (!isNaN(x) && !isNaN(y)) {
  //     const coordinate = { x, y };
  //     hoverCell(x, y);
  //     onCellHover?.(coordinate, event);
  //   }
  // }, [hoverCell, onCellHover]);

  // 处理焦点
  const handleCellFocus = useCallback((event: React.FocusEvent) => {
    const target = event.target as HTMLElement;
    const x = parseInt(target.dataset.x || '0', 10);
    const y = parseInt(target.dataset.y || '0', 10);

    if (!isNaN(x) && !isNaN(y)) {
      const coordinate = { x, y };
      focusCell(x, y);
      onCellFocus?.(coordinate, event);
    }
  }, [focusCell, onCellFocus]);



  // 使用统一的样式配置
  const viewportStyle = getViewportStyle(style);
  const containerStyle = getContainerStyle();



  // 在客户端渲染完成前显示占位符
  if (!isClient) {
    return (
      <div
        className={`matrix-viewport ${className}`}
        style={{ ...viewportStyle, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
      >
        <div className="text-gray-500">矩阵加载中...</div>
      </div>
    );
  }

  return (
    <div
      className={`matrix-viewport ${className}`}
      style={viewportStyle}
    >
      <div
        ref={containerRef}
        className="matrix-container"
        style={containerStyle}
        onClick={handleCellClick}
        onDoubleClick={handleCellDoubleClick}
        onFocus={handleCellFocus}
        tabIndex={0}
        role="grid"
        aria-label="矩阵网格"
        aria-rowcount={33}
        aria-colcount={33}
      >
        {renderMatrixCells()}
      </div>
    </div>
  );
};

// ===== 性能优化 =====

const Matrix = memo(MatrixComponent);

Matrix.displayName = 'Matrix';

export default Matrix;
