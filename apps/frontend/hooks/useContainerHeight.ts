/**
 * 容器高度计算Hook
 * 🎯 核心价值：智能计算页面剩余高度，确保词库容器最优显示
 * 📦 功能范围：动态高度计算、响应式调整、容器尺寸监听
 * 🔄 架构设计：基于窗口尺寸和固定元素的自动化高度计算
 */

'use client';

import { useCallback, useEffect, useRef, useState } from 'react';

// ===== Hook接口 =====

interface UseContainerHeightOptions {
  /** 固定元素的选择器数组（如标题栏、状态栏等） */
  fixedElementSelectors?: string[];
  /** 额外的边距（padding、margin等） */
  extraMargin?: number;
  /** 最小高度 */
  minHeight?: number;
  /** 最大高度 */
  maxHeight?: number;
  /** 是否启用调试模式 */
  debug?: boolean;
}

interface UseContainerHeightReturn {
  /** 计算出的容器高度 */
  containerHeight: number;
  /** 窗口高度 */
  windowHeight: number;
  /** 是否正在计算 */
  isCalculating: boolean;
  /** 手动重新计算 */
  recalculate: () => void;
  /** 容器引用（用于绑定到目标容器） */
  containerRef: React.RefObject<HTMLDivElement>;
}

// ===== 主Hook =====

export const useContainerHeight = (options: UseContainerHeightOptions = {}): UseContainerHeightReturn => {
  const {
    fixedElementSelectors = [],
    extraMargin = 0,
    minHeight = 200,
    maxHeight,
    debug = false
  } = options;

  const [state, setState] = useState({
    containerHeight: minHeight,
    windowHeight: 0,
    isCalculating: false,
    isClient: false
  });

  const containerRef = useRef<HTMLDivElement>(null);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 计算容器高度的核心函数
  const calculateHeight = useCallback(() => {
    if (!state.isClient || !containerRef.current) {
      return;
    }

    setState(prev => ({ ...prev, isCalculating: true }));

    try {
      const windowHeight = window.innerHeight;
      const containerRect = containerRef.current.getBoundingClientRect();
      
      // 计算固定元素占用的高度
      let fixedElementsHeight = 0;
      fixedElementSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
          const rect = element.getBoundingClientRect();
          fixedElementsHeight += rect.height;
        });
      });

      // 计算容器顶部到窗口顶部的距离
      const containerTop = containerRect.top;
      
      // 计算可用高度：窗口高度 - 容器顶部位置 - 固定元素高度 - 额外边距
      let availableHeight = windowHeight - containerTop - fixedElementsHeight - extraMargin;
      
      // 应用最小和最大高度限制
      availableHeight = Math.max(availableHeight, minHeight);
      if (maxHeight) {
        availableHeight = Math.min(availableHeight, maxHeight);
      }

      if (debug) {
        console.log('Height calculation:', {
          windowHeight,
          containerTop,
          fixedElementsHeight,
          extraMargin,
          availableHeight,
          minHeight,
          maxHeight
        });
      }

      setState(prev => ({
        ...prev,
        containerHeight: Math.floor(availableHeight),
        windowHeight,
        isCalculating: false
      }));
    } catch (error) {
      console.error('Error calculating container height:', error);
      setState(prev => ({
        ...prev,
        containerHeight: minHeight,
        isCalculating: false
      }));
    }
  }, [state.isClient, fixedElementSelectors, extraMargin, minHeight, maxHeight, debug]);

  // 防抖的重新计算函数
  const debouncedCalculate = useCallback(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
    
    debounceTimeoutRef.current = setTimeout(() => {
      calculateHeight();
    }, 100);
  }, [calculateHeight]);

  // 手动重新计算
  const recalculate = useCallback(() => {
    calculateHeight();
  }, [calculateHeight]);

  // 确保客户端渲染
  useEffect(() => {
    setState(prev => ({ ...prev, isClient: true }));
  }, []);

  // 窗口尺寸变化监听
  useEffect(() => {
    if (!state.isClient) return;

    const handleResize = () => {
      debouncedCalculate();
    };

    // 初始计算
    calculateHeight();

    // 添加监听器
    window.addEventListener('resize', handleResize);

    // 清理
    return () => {
      window.removeEventListener('resize', handleResize);
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [state.isClient, debouncedCalculate, calculateHeight]);

  // 监听容器位置变化（使用ResizeObserver和MutationObserver）
  useEffect(() => {
    if (!state.isClient || !containerRef.current) return;

    const container = containerRef.current;
    
    // ResizeObserver监听容器尺寸变化
    const resizeObserver = new ResizeObserver(() => {
      debouncedCalculate();
    });

    // MutationObserver监听DOM结构变化
    const mutationObserver = new MutationObserver(() => {
      debouncedCalculate();
    });

    resizeObserver.observe(container);
    mutationObserver.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class']
    });

    return () => {
      resizeObserver.disconnect();
      mutationObserver.disconnect();
    };
  }, [state.isClient, debouncedCalculate]);

  return {
    containerHeight: state.containerHeight,
    windowHeight: state.windowHeight,
    isCalculating: state.isCalculating,
    recalculate,
    containerRef
  };
};

// ===== 预设配置 =====

/** 词库管理器的高度计算配置 */
export const WORD_MANAGER_HEIGHT_CONFIG: UseContainerHeightOptions = {
  fixedElementSelectors: [
    '.controls-container .flex-shrink-0', // 控制面板顶部固定区域
    '.status-bar', // 状态栏
    '.word-library-manager h3', // 词库管理标题
    '.word-library-manager .flex-shrink-0' // 词库管理器顶部按钮区域
  ],
  extraMargin: 32, // 额外边距（padding等）
  minHeight: 300,
  debug: false
};

export default useContainerHeight;
